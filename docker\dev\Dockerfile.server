FROM node:20-alpine
RUN corepack enable

WORKDIR /app

# Copy package files for dependency installation
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY packages/server/package.json ./packages/server/
COPY packages/shared ./packages/shared

# Install dependencies
RUN pnpm install --frozen-lockfile

# Don't copy source code - it will be mounted as volume
# Create the working directory
WORKDIR /app/packages/server

# Expose the port
EXPOSE 3000 9229
