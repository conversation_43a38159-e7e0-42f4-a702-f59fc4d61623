services:
  mongo:
    image: mongo:8
    ports:
      - 27017:27017
    env_file:
      - ../.env
    volumes:
      - mongo_data:/data/db

  server:
    build:
      context: ../
      dockerfile: docker/dev/Dockerfile.server
    image: base-server-dev:latest
    env_file:
      - ../.env
    ports:
      - 3001:3001
      - 9229:9229 # Debug port
    depends_on:
      - mongo
    volumes:
      # Mount source code as volumes for instant updates
      - ../packages/server:/app/packages/server
      - ../packages/shared:/app/packages/shared
      # Exclude node_modules to avoid conflicts
      - /app/packages/server/node_modules
      - /app/node_modules
    command: sh -c "pnpm start:debug"
    develop:
      watch:
        # Watch for package.json changes to trigger rebuild
        - action: rebuild
          path: ../packages/server/package.json
        - action: rebuild
          path: ../pnpm-lock.yaml
        # Sync shared package changes
        - action: sync
          path: ../packages/shared
          target: /app/packages/shared
          ignore:
            - node_modules
  client:
    build:
      context: ../
      dockerfile: docker/dev/Dockerfile.client
    image: base-client-dev:latest
    env_file:
      - ../.env
    ports:
      - 5173:5173
    depends_on:
      - server
    volumes:
      # Mount source code as volumes for instant updates
      - ../packages/client:/app/packages/client
      - ../packages/shared:/app/packages/shared
      # Exclude node_modules to avoid conflicts
      - /app/packages/client/node_modules
      - /app/node_modules
    command: sh -c "pnpm dev"
    develop:
      watch:
        - action: sync
          path: ../packages/client
          target: /app/packages/client
          ignore:
            - node_modules
        # Watch for package.json changes to trigger rebuild
        - action: rebuild
          path: ../packages/client/package.json
        - action: rebuild
          path: ../pnpm-lock.yaml
        # Sync shared package changes
        - action: sync
          path: ../packages/shared
          target: /app/packages/shared
          ignore:
            - node_modules

volumes:
  mongo_data:
  #pg_data:
