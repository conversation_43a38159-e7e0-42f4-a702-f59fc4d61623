services:
  mongo:
    image: mongo:8
    ports:
      - 27017:27017
    env_file:
      - ../.env
    volumes:
      - mongo_data:/data/db

  server:
    build:
      context: ../
      dockerfile: docker/dev/Dockerfile.server
    image: base-server-dev:latest
    env_file:
      - ../.env
    ports:
      - 3001:3001
      - 9229:9229 # Add debug port
    depends_on:
      - mongo
    command: sh -c "pnpm start:debug"
    develop:
      watch:
        - action: sync
          path: ../packages/server
          target: /app/packages/server
          ignore:
            - node_modules
        - action: sync
          path: ../packages/shared
          target: /app/packages/server/node_modules/@base/shared
        - action: rebuild
          path: ../pnpm-lock.yaml
  client:
    build:
      context: ../
      dockerfile: docker/dev/Dockerfile.client
    image: base-client-dev:latest
    env_file:
      - ../.env
    ports:
      - 5173:5173
      - 9222:9222 # Add debug port for React
    depends_on:
      - server
    command: sh -c "pnpm dev"
    develop:
      watch:
        - action: sync
          path: ../packages/client
          target: /app/packages/client
          ignore:
            - node_modules
        - action: rebuild
          path: ../pnpm-lock.yaml
        - action: sync
          path: ../packages/shared
          target: /app/packages/client/node_modules/@base/shared

volumes:
  mongo_data:
  #pg_data:
