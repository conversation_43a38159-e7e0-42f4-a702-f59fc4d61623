# Builder stage
FROM node:20-alpine AS builder
RUN corepack enable

WORKDIR /app
# Copy root package.json
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY packages/server ./packages/server
COPY packages/shared ./packages/shared
# Install both dev and prod dependencies for pnpm build (needs Typescript which is dev dep) and npx prisma generate (needs prisma cli)
RUN pnpm install --frozen-lockfile --ignore-scripts

WORKDIR /app/packages/server
COPY packages/server/prisma ./prisma
RUN npx prisma generate
RUN pnpm build

# Production stage
FROM node:20-alpine
RUN corepack enable

WORKDIR /app

COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY packages/server/package.json ./packages/server/
COPY packages/shared ./packages/shared
RUN pnpm install --frozen-lockfile --prod --ignore-scripts

WORKDIR /app/packages/server

COPY --from=builder /app/packages/server/dist ./dist
# Copy Prisma schema for runtime use
COPY packages/server/prisma ./prisma

COPY packages/server/entrypoint.sh .
RUN chmod +x ./entrypoint.sh

# Add a non-root user for security and switch to it
RUN addgroup -S appgroup && \
    adduser -S appuser -G appgroup && \
    chown -R appuser:appgroup /app
USER appuser

ENV NODE_ENV=production
EXPOSE 3000

CMD ["./entrypoint.sh"]
